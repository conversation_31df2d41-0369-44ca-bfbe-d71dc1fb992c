import React from 'react';
import {JobAlertIcon} from '../utils/svgIcons';

type CellRendererProps = {
  type: 'risk' | 'template';
  original: {
    job_step: string;
    risk_job_residual_risk_rating?: any[];
    template_job_residual_risk_rating?: any[];
  };
};

export const RiskJobCell: React.FC<CellRendererProps> = ({type, original}) => {
  const residualRiskRating =
    type === 'risk'
      ? original.risk_job_residual_risk_rating
      : original.template_job_residual_risk_rating;

  const hasRiskAlert = residualRiskRating?.some(
    (rating: any) => rating?.reason?.length,
  );

  return (
    <div className="gap-3 d-flex align-items-center">
      <div className="d-flex align-items-center mw-32 pr-16px">
        {hasRiskAlert && <JobAlertIcon />}
      </div>
      {original.job_step}
    </div>
  );
};
