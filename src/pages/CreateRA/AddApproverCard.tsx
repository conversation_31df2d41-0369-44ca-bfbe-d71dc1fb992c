import React, {useCallback, useMemo, useState} from 'react';
import {Card, Dropdown} from 'react-bootstrap';
import {toast} from 'react-toastify';
import {orderBy} from 'lodash';
import {ExclaimationIcon} from '../../utils/svgIcons';
import {RaLevel, RAStatus} from '../../enums';
import {AsyncSearchCrewMember} from '../../components/SearchCrewMember';
import ColoredTile, {ColoredTileTheme} from '../../components/ColoredTile';
import {cleanObject, getErrorMessage, parseDate} from '../../utils/common';
import {JwtUser, OfficeApprover, RAItemFull} from '../../types';
import {
  approveOrRejectRA,
  assignApproversToRA,
  getOfficeApprovers,
  reAssignApprover,
} from '../../services/services';
import {getInitials} from '../../utils/user';
import {SvgUserOutline} from '../../components/icons';
import ReAssignApproverModal from './ReAssignApproverModal';
import RAApprovalModal, {
  operationTypeToApprovalStatus,
  RAApprovalModalProps,
} from './RAApprovalModal';
import {useDataStoreContext} from '../../context';

import '../../styles/components/add-approver-card.scss';

const getApproverStatusText = (
  approver?: RAItemFull['risk_approver'][0],
): [string, ColoredTileTheme] => {
  if (approver?.status === 1 && approver?.approval_status === 3) {
    return ['Approved with Condition', 'green'];
  }
  if (approver?.status === 1 && approver?.approval_status === 1) {
    return ['Approved', 'green'];
  }
  if (approver?.status === 1 && approver?.approval_status === 2) {
    return ['Rejected', 'red'];
  }
  return ['Pending', 'yellow'];
};

const getRaStatusText = (
  status: RAStatus,
  approvers: RAItemFull['risk_approver'],
): [string | undefined, ColoredTileTheme | undefined] => {
  if ([RAStatus.DRAFT, RAStatus.PUBLISHED].includes(status)) {
    return ['Pending', 'yellow'];
  }

  if (status === RAStatus.APPROVED) {
    const sortedApprovers = approvers
      .filter(approver => approver.approval_order)
      .sort((a, b) => (a.approval_order ?? 0) - (b.approval_order ?? 0));

    const containConditionalApprover = sortedApprovers.some(
      approver => approver.message,
    );

    return [
      containConditionalApprover ? 'Approved with Condition' : 'Approved',
      'green',
    ];
  }

  if (status === RAStatus.REJECTED) {
    return ['Rejected', 'red'];
  }

  return [undefined, undefined];
};

interface AddApproverCardProps {
  riskId: number;
  raStatus: RAStatus;
  raLevel?: RaLevel;
  existingApprovers?: RAItemFull['risk_approver'];
  refetchRA: () => void;
}

type SelectedApprovers = Record<number, OfficeApprover | null>;

const AddApproverCard: React.FC<AddApproverCardProps> = ({
  riskId,
  raStatus,
  raLevel,
  existingApprovers,
  refetchRA,
}) => {
  const [selectedApprovers, setSelectedApprovers] = useState<SelectedApprovers>(
    {1: null, 2: null, 3: null},
  );
  const {
    roleConfig: {user},
  } = useDataStoreContext();

  const $content = useMemo(() => {
    if (!raLevel) {
      return (
        <div className="w-100 h-100 d-flex flex-column align-items-center justify-content-center gap-16px">
          <ExclaimationIcon />
          <div className="fs-14 text-muted text-center">
            Can only be added once you <br />
            submit the RA for Approval
          </div>
        </div>
      );
    }

    if (
      [RaLevel.CRITICAL, RaLevel.SPECIAL].includes(raLevel) &&
      [RAStatus.PUBLISHED, RAStatus.APPROVED, RAStatus.REJECTED].includes(
        raStatus,
      )
    ) {
      return (
        <AssignApprovers
          approvers={selectedApprovers}
          onChange={setSelectedApprovers}
          existingApprovers={existingApprovers}
          riskId={riskId}
          refetchRA={refetchRA}
          user={user}
          raStatus={raStatus}
        />
      );
    }

    if (raLevel === RaLevel.ROUTINE) {
      const defaultApprover = existingApprovers?.find(
        user => user.approval_order === null,
      );
      return (
        <div className="assign-approvers-container">
          <div className="assign-approver">
            <ExistingApprover
              existingApprover={defaultApprover ?? {}}
              user={user}
              raStatus={raStatus}
            />
          </div>
        </div>
      );
    }

    return null;
  }, [
    raLevel,
    raStatus,
    riskId,
    JSON.stringify(selectedApprovers),
    setSelectedApprovers,
    existingApprovers,
    user,
    refetchRA,
  ]);

  const assignApprovers = async (
    param: Parameters<typeof assignApproversToRA>[0],
  ) => {
    try {
      const {message} = await assignApproversToRA(param);
      toast.success(message || 'Approvers assigned successfully!');
      refetchRA();
    } catch (error) {
      toast.error('Failed to assign approvers. Please try again.');
    }
  };

  const selectedUserIds = Object.values(selectedApprovers)
    .filter(user => user)
    .map(user => user?.user_id)
    .filter(Boolean);
  const allAssigneseSelected = selectedUserIds.length === 3;
  const existingApproverUser = existingApprovers?.filter(
    user => user.approval_order,
  );
  const [statusText, statusColor] = getRaStatusText(
    raStatus,
    existingApproverUser || [],
  );

  return (
    <Card className="assign-approvers-card h-390p">
      <Card.Title className="d-flex align-items-center justify-content-between p-16px">
        <div className="fs-16 fw-600">Office Approval</div>
        <div className="d-flex align-items-center justify-content-between">
          {allAssigneseSelected && (existingApproverUser?.length ?? []) === 0 && (
            <button
              className="assign-btn mr-2"
              disabled={!allAssigneseSelected}
              onClick={() =>
                assignApprovers({
                  risk_id: riskId,
                  approvers: Object.entries(selectedApprovers).map(
                    ([key, approver]) => ({
                      keycloak_id: approver?.user_id || '',
                      order: Number(key),
                      rank: approver?.rank,
                    }),
                  ),
                })
              }
            >
              Assign
            </button>
          )}
          {statusText && statusColor && (
            <ColoredTile text={statusText} theme={statusColor} />
          )}
        </div>
      </Card.Title>
      <hr />
      <div className="card-body">{$content}</div>
    </Card>
  );
};

export default AddApproverCard;

export {
  getApproverStatusText,
  getRaStatusText,
  AssignApprovers,
  ExistingApprover,
};

interface AssignApproversProps
  extends Pick<AddApproverCardProps, 'riskId' | 'refetchRA' | 'raStatus'> {
  approvers: SelectedApprovers;
  onChange: React.Dispatch<React.SetStateAction<SelectedApprovers>>;
  existingApprovers?: RAItemFull['risk_approver'];
  user: JwtUser;
}

const AssignApprovers: React.FC<AssignApproversProps> = ({
  approvers,
  onChange,
  existingApprovers,
  riskId,
  refetchRA,
  user,
  raStatus,
}) => {
  const fetchOfficeApprovers = async (search?: string) => {
    if (!search || search.trim().length < 3) {
      return {options: [], originalData: []};
    }

    const data = await getOfficeApprovers(search);
    const selectedUserIds = Object.values(approvers)
      .map(approver => approver?.user_id)
      .filter(Boolean);
    const existingApproverIds =
      existingApprovers
        ?.filter(approver => approver.approval_order) // Filter out default user
        ?.map(approver => approver.keycloak_id) || [];

    return {
      options: data
        .filter(
          approver =>
            !selectedUserIds.includes(approver.user_id) &&
            !existingApproverIds.includes(approver.user_id),
        )
        .map(approver => ({
          id: approver.user_id,
          full_name: [approver.first_name, approver.last_name]
            .filter(Boolean)
            .join(' '),
          subText: [approver.email, approver.rank].filter(Boolean).join(' • '),
        })),
      originalData: data,
    };
  };

  const reAssignApprove = useCallback(
    async (approver: OfficeApprover, order: number) => {
      try {
        const {message} = await reAssignApprover({
          risk_id: riskId,
          approval_order: order,
          new_keycloak_id: approver.user_id,
          rank: approver.rank,
        });
        refetchRA?.();

        toast.success(message || 'Reviewer re-assigned successfully!');
      } catch (error) {
        toast.error(
          getErrorMessage(
            error,
            'Failed to re-assign reviewer. Please try again.',
          ),
        );
      }
    },
    [reAssignApprover, riskId],
  );

  const approveOrRejectRisk = useCallback(
    async (params: Parameters<RAApprovalModalProps['onConfirm']>['0']) => {
      const result = await approveOrRejectRA(
        cleanObject({
          risk_id: riskId,
          message: params.comment || undefined,
          action_date: parseDate(params.actionDate, 'YYYY-MM-DD'),
          status: operationTypeToApprovalStatus[params.operationType],
          keycloak_id: user.user_id,
        }) as Parameters<typeof approveOrRejectRA>[0],
      );

      refetchRA?.();
      return result;
    },
    [riskId, user.user_id, refetchRA],
  );

  const assignedApproversSorted = orderBy(
    (existingApprovers || []).filter(approver => approver.approval_order),
    ['approval_order'],
  );

  return (
    <div className="assign-approvers-container">
      {['First Approver', 'Second Approver', 'Final Approver'].map(
        (reviewer, index) => {
          const position = index + 1;
          const currentApprover = approvers[position];
          const existingApprover = assignedApproversSorted[index];
          const [approverStatusText, theme] =
            getApproverStatusText(existingApprover);
          const isLoggedInUserOneOfTheApprovers = assignedApproversSorted?.find(
            approver => approver.keycloak_id === user.user_id,
          );
          const userCanBeReAssigned =
            [1, 2].includes(existingApprover?.status) &&
            existingApprover.approval_status === null &&
            isLoggedInUserOneOfTheApprovers?.approval_status === null &&
            raStatus === RAStatus.PUBLISHED;

          return (
            <div key={reviewer} className="assign-approver">
              <div className="d-flex align-items-center justify-content-between">
                <div className="approver-title mr-1">{reviewer}</div>
                {existingApprover && (
                  <ColoredTile
                    text={approverStatusText}
                    theme={theme}
                    className="mr-1"
                  />
                )}
                {userCanBeReAssigned && (
                  <ReAssignApproverModal
                    trigger={
                      <button className="ra-no-style-btn">
                        <SvgUserOutline />
                      </button>
                    }
                    fetchQuery={fetchOfficeApprovers}
                    approverOrder={position}
                    onConfirm={reAssignApprove}
                  />
                )}
              </div>
              {existingApprover ? (
                <ExistingApprover
                  assignedApproversSorted={assignedApproversSorted}
                  index={index}
                  existingApprover={existingApprover}
                  approveOrRejectRisk={approveOrRejectRisk}
                  user={user}
                  raStatus={raStatus}
                />
              ) : (
                <div className="assign-approver-input">
                  <AsyncSearchCrewMember<OfficeApprover>
                    value={
                      currentApprover?.email ? [currentApprover.email] : []
                    }
                    placeholder="Search Name, Rank or Email"
                    onChange={(selectedUserIds, originalData) => {
                      const selectedUserId = selectedUserIds[0];

                      if (selectedUserId && originalData?.length) {
                        const selectedUser = originalData.find(
                          user => user.user_id === selectedUserId,
                        );
                        if (selectedUser) {
                          onChange(prev => ({
                            ...prev,
                            [position]: selectedUser,
                          }));
                        }
                      } else {
                        onChange(prev => ({...prev, [position]: null}));
                      }
                    }}
                    fetchQuery={fetchOfficeApprovers}
                    uniqueQueryKey={`getOfficeApprovers-${index}`}
                  />
                </div>
              )}
            </div>
          );
        },
      )}
    </div>
  );
};

interface ExistingApproverProps {
  assignedApproversSorted?: RAItemFull['risk_approver'];
  existingApprover: Partial<RAItemFull['risk_approver'][0]>;
  index?: number;
  user: JwtUser;
  approveOrRejectRisk?: (
    params: Parameters<RAApprovalModalProps['onConfirm']>['0'],
  ) => Promise<{message: string}>;
  raStatus: RAStatus;
}

const ExistingApprover: React.FC<ExistingApproverProps> = ({
  assignedApproversSorted,
  existingApprover,
  index,
  approveOrRejectRisk,
  user,
  raStatus,
}) => {
  let canPerformAction =
    existingApprover.status === 1 &&
    existingApprover.approval_status === null &&
    user.user_id === existingApprover.keycloak_id &&
    raStatus === RAStatus.PUBLISHED;

  // Check if the previous approver has approved or rejected
  if (assignedApproversSorted && index !== undefined) {
    canPerformAction =
      canPerformAction &&
      (!assignedApproversSorted[index - 1] ||
        [1, 3].includes(
          assignedApproversSorted[index - 1]?.approval_status ?? 0,
        ));
  }

  const nextReviewTitle = useMemo(() => {
    if (index === 0) {
      return 'Second';
    }
    if (index === 1) {
      return 'Final';
    }
  }, [index]);

  return (
    <>
      <div className="user-list-item">
        <div className="avatar">{getInitials(existingApprover.user_name)}</div>
        <div className="d-flex align-items-center justify-content-between w-100">
          <div className="user-info">
            <div className="user-name">
              {existingApprover.user_name}{' '}
              {existingApprover.keycloak_id === user.user_id ? '(Myself)' : ''}
            </div>
            <div className="user-details">
              {[
                existingApprover.job_title,
                atob(existingApprover?.user_email || ''),
              ]
                .filter(Boolean)
                .join(' • ')}
            </div>
          </div>
          {canPerformAction && (
            <div className="d-flex align-items-center justify-content-center">
              <RAApprovalModal
                operationType="reject"
                trigger={<button className="assign-btn mr-2">Reject</button>}
                onConfirm={
                  approveOrRejectRisk as unknown as RAApprovalModalProps['onConfirm']
                }
              />
              <Dropdown className="approval-btn">
                <Dropdown.Toggle variant="primary">Approval</Dropdown.Toggle>
                <Dropdown.Menu>
                  <RAApprovalModal
                    operationType="approve"
                    trigger={<Dropdown.Item>Approve</Dropdown.Item>}
                    onConfirm={
                      approveOrRejectRisk as unknown as RAApprovalModalProps['onConfirm']
                    }
                    reviewIndex={index}
                    reviewerTitle={nextReviewTitle}
                  />
                  <RAApprovalModal
                    operationType="approveWithComment"
                    trigger={
                      <Dropdown.Item>Approve with Condition</Dropdown.Item>
                    }
                    onConfirm={
                      approveOrRejectRisk as unknown as RAApprovalModalProps['onConfirm']
                    }
                    reviewIndex={index}
                    reviewerTitle={nextReviewTitle}
                  />
                </Dropdown.Menu>
              </Dropdown>
            </div>
          )}
        </div>
      </div>
      {existingApprover.message && (
        <div>
          <div className="condition-for-action-heading">
            {existingApprover.approval_status === 2
              ? 'Reason for Rejection'
              : 'Condition for Approval'}
          </div>
          <div className="condition-for-action-content">
            {existingApprover.message}
          </div>
        </div>
      )}
    </>
  );
};
